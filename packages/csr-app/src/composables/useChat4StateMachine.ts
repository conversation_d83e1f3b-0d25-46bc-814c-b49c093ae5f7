/**
 * Chat4 状态机
 * 管理 Chat4 中复杂的场景切换逻辑
 */

import { ref, computed } from 'vue'
import { useChatEventsStore } from '@/store/chat-events'
import { useChatMessagesStore } from '@/store/chat-messages'
import { useStoryStore } from '@/store/story'
import { useUserStore } from '@/store/user'
import { useRechargeStore } from '@/store/recharge'
import { jumpToSceneSSE } from '@/services/chat'
import { reportEvent } from '@/utils/report'
import { ReportEvent } from '@/interface'
import {
  Chat4SceneId,
  Chat4SceneState,
  Chat4SceneUtils,
} from '@/types/chat4-scene'
import { useChat4Store } from '@/store/chat4'
import { useScenePayment } from '@/mobile/views/chat4/composables/useScenePayment'
import { useChat4Messages } from '@/mobile/views/chat4/composables/useChat4Messages'

/**
 * 场景切换动作枚举
 */
export enum Chat4Action {
  // 基础导航
  GO_TO_LIVE = 'GO_TO_LIVE',
  GO_TO_CHAT = 'GO_TO_CHAT',
  GO_TO_VIDEO = 'GO_TO_VIDEO',
  GO_TO_MONITOR = 'GO_TO_MONITOR',
  GO_TO_MAP = 'GO_TO_MAP',
  GO_TO_MEETUP = 'GO_TO_MEETUP',
  GO_TO_DANCING = 'GO_TO_DANCING',
  GO_TO_CONCERT = 'GO_TO_CONCERT',
  GO_TO_TIP = 'GO_TO_TIP',
  GO_TO_GAME = 'GO_TO_GAME',
  GO_TO_SETTINGS = 'GO_TO_SETTINGS',
  GO_TO_MOMENT = 'GO_TO_MOMENT',
  GO_TO_DIARY = 'GO_TO_DIARY',

  // 特殊动作
  ACCEPT_FRIEND_REQUEST = 'ACCEPT_FRIEND_REQUEST',
  REJECT_FRIEND_REQUEST = 'REJECT_FRIEND_REQUEST',
  HANGUP_VIDEO = 'HANGUP_VIDEO',
  BACK_TO_PREVIOUS = 'BACK_TO_PREVIOUS',

  // 服务器事件
  SERVER_SCENE_CHANGE = 'SERVER_SCENE_CHANGE',

  // 错误处理
  HANDLE_ERROR = 'HANDLE_ERROR',
  RESET_TO_DEFAULT = 'RESET_TO_DEFAULT',
}

/**
 * Loading 类型定义
 */
export type Chat4LoadingType =
  | 'scene-transition'
  | 'message-loading'
  | 'connection'
  | 'general'

/**
 * 状态机状态定义
 */
export interface Chat4State {
  /** 当前场景 */
  currentScene: Chat4SceneState
  /** 上一个场景（用于返回） */
  previousScene: Chat4SceneState | null
  /** 场景历史栈 */
  sceneHistory: Chat4SceneState[]
  /** 是否正在切换场景 */
  isTransitioning: boolean
  /** 是否显示加载状态 */
  isLoading: boolean
  /** 加载类型 */
  loadingType: Chat4LoadingType
  /** 自定义加载标题 */
  loadingTitle?: string
  /** 自定义加载副标题 */
  loadingSubtitle?: string
  /** 错误状态 */
  error: string | null
  /** 服务器记录的场景ID */
  serverSceneId: string | null
  /** 是否可以返回上一个场景 */
  canGoBack: boolean
}

/**
 * 场景切换配置
 */
export interface SceneTransition {
  from: Chat4SceneState | '*'
  to: Chat4SceneState
  action: Chat4Action
  guard?: () => boolean
  effect?: () => void | Promise<void>
  clearMessages?: boolean // 是否在切换时清空消息
}

/**
 * Chat4 状态机
 */
export function useChat4StateMachine() {
  const chatEventsStore = useChatEventsStore()
  const chatMessagesStore = useChatMessagesStore()
  const storyStore = useStoryStore()
  const userStore = useUserStore()
  const rechargeStore = useRechargeStore()

  // 状态定义
  const state = ref<Chat4State>({
    currentScene: Chat4SceneId.LIVING,
    previousScene: null,
    sceneHistory: [Chat4SceneId.LIVING],
    isTransitioning: false,
    isLoading: true, // 初始化时显示加载状态
    loadingType: 'connection', // 初始加载类型为连接
    error: null,
    serverSceneId: null,
    canGoBack: false,
  })

  // 场景切换规则定义
  const transitions: SceneTransition[] = [
    // 从直播间到其他场景
    {
      from: Chat4SceneId.LIVING,
      to: Chat4SceneId.PHONE,
      action: Chat4Action.GO_TO_CHAT,
      guard: () =>
        !!storyStore.currentStory?.id && !!storyStore.currentActor?.id,
      clearMessages: true, // 进入聊天室时清空消息
    },
    {
      from: Chat4SceneId.LIVING,
      to: Chat4SceneId.PHONE,
      action: Chat4Action.ACCEPT_FRIEND_REQUEST,
      guard: () =>
        !!storyStore.currentStory?.id && !!storyStore.currentActor?.id,
      clearMessages: true, // 接受好友请求进入聊天室时清空消息
    },

    // 从聊天室到其他场景
    {
      from: Chat4SceneId.PHONE,
      to: Chat4SceneId.LIVING,
      action: Chat4Action.GO_TO_LIVE,
      clearMessages: true,
    },

    {
      from: Chat4SceneId.PHONE,
      to: Chat4SceneId.VIDEO_CALL,
      action: Chat4Action.GO_TO_VIDEO,
      guard: () => !!userStore.userId,
      clearMessages: true, // 进入视频通话时清空消息
    },

    // 从视频通话到其他场景
    {
      from: Chat4SceneId.VIDEO_CALL,
      to: Chat4SceneId.PHONE,
      action: Chat4Action.GO_TO_CHAT,
      clearMessages: true,
    },
    {
      from: Chat4SceneId.PHONE,
      to: Chat4SceneId.MONITOR,
      action: Chat4Action.GO_TO_MONITOR,
      guard: () => !!userStore.userId,
      clearMessages: true, // 进入监控场景时清空消息
    },
    {
      from: Chat4SceneId.PHONE,
      to: Chat4SceneId.MAP,
      action: Chat4Action.GO_TO_MAP,
      guard: () => !!userStore.userId,
      clearMessages: true, // 进入地图场景时清空消息
    },
    {
      from: Chat4SceneId.PHONE,
      to: Chat4SceneId.MAP,
      action: Chat4Action.GO_TO_MEETUP,
      guard: () => !!userStore.userId,
      clearMessages: true, // 进入地图场景时清空消息
    },
    {
      from: Chat4SceneId.PHONE,
      to: Chat4SceneId.TIP,
      action: Chat4Action.GO_TO_TIP,
      clearMessages: false, // 打赏场景不清空消息
      effect: () => {
        // if (userStore.userInfo?.role === 'guest') {
        //   // 游客用户显示登录弹窗
        //   // showAuthDrawer.value = true
        // } else {
        rechargeStore.toggleRechargeModal()
        // }
      },
    },
    {
      from: Chat4SceneId.PHONE,
      to: Chat4SceneId.MONITOR,
      action: Chat4Action.GO_TO_MONITOR,
      clearMessages: true,
    },
    {
      from: Chat4SceneId.PHONE,
      to: Chat4SceneId.MOMENT,
      action: Chat4Action.GO_TO_MOMENT,
      clearMessages: true, // 进入朋友圈时清空消息
    },
    {
      from: Chat4SceneId.PHONE,
      to: Chat4SceneId.DIARY,
      action: Chat4Action.GO_TO_DIARY,
      clearMessages: true, // 进入日记场景时清空消息
    },

    // 从监控场景到其他场景
    {
      from: Chat4SceneId.MONITOR,
      to: Chat4SceneId.PHONE,
      action: Chat4Action.GO_TO_CHAT,
      clearMessages: true,
    },

    // Map 场景切换
    {
      from: Chat4SceneId.PHONE,
      to: Chat4SceneId.MAP,
      action: Chat4Action.GO_TO_MAP,
      clearMessages: true, // 地图场景清空消息
    },

    // 从地图场景到其他场景
    {
      from: Chat4SceneId.MAP,
      to: Chat4SceneId.PHONE,
      action: Chat4Action.GO_TO_CHAT,
      clearMessages: true,
    },

    // 从地图场景到各个meetup子场景
    {
      from: Chat4SceneId.MAP,
      to: Chat4SceneId.MEETUP_POOL,
      action: Chat4Action.GO_TO_MEETUP,
      clearMessages: true,
    },
    {
      from: Chat4SceneId.MAP,
      to: Chat4SceneId.MEETUP_COFFEE,
      action: Chat4Action.GO_TO_MEETUP,
      clearMessages: true,
    },
    {
      from: Chat4SceneId.MAP,
      to: Chat4SceneId.MEETUP_OFFICE,
      action: Chat4Action.GO_TO_MEETUP,
      clearMessages: true,
    },
    {
      from: Chat4SceneId.MAP,
      to: Chat4SceneId.MEETUP_SEASIDE,
      action: Chat4Action.GO_TO_MEETUP,
      clearMessages: true,
    },

    // Meetup 场景切换
    {
      from: Chat4SceneId.PHONE,
      to: Chat4SceneId.MEETUP,
      action: Chat4Action.GO_TO_MEETUP,
      clearMessages: true, // 约会场景清空消息
    },

    // 从约会场景到其他场景（包括返回地图）
    {
      from: Chat4SceneId.MEETUP,
      to: Chat4SceneId.MAP,
      action: Chat4Action.GO_TO_MAP,
      clearMessages: true,
    },
    {
      from: Chat4SceneId.MEETUP_POOL,
      to: Chat4SceneId.MAP,
      action: Chat4Action.GO_TO_MAP,
      clearMessages: true,
    },
    {
      from: Chat4SceneId.MEETUP_COFFEE,
      to: Chat4SceneId.MAP,
      action: Chat4Action.GO_TO_MAP,
      clearMessages: true,
    },
    {
      from: Chat4SceneId.MEETUP_OFFICE,
      to: Chat4SceneId.MAP,
      action: Chat4Action.GO_TO_MAP,
      clearMessages: true,
    },
    {
      from: Chat4SceneId.MEETUP_SEASIDE,
      to: Chat4SceneId.MAP,
      action: Chat4Action.GO_TO_MAP,
      clearMessages: true,
    },
    {
      from: Chat4SceneId.MEETUP,
      to: Chat4SceneId.PHONE,
      action: Chat4Action.GO_TO_CHAT,
      clearMessages: true,
    },

    // 从朋友圈场景到其他场景
    {
      from: Chat4SceneId.MOMENT,
      to: Chat4SceneId.PHONE,
      action: Chat4Action.GO_TO_CHAT,
      clearMessages: true,
    },

    // Dancing 场景切换
    {
      from: Chat4SceneId.PHONE,
      to: Chat4SceneId.DANCING,
      action: Chat4Action.GO_TO_DANCING,
      clearMessages: true,
    },

    // 从舞蹈场景到其他场景
    {
      from: Chat4SceneId.DANCING,
      to: Chat4SceneId.PHONE,
      action: Chat4Action.GO_TO_CHAT,
      clearMessages: true,
    },

    // Concert 场景切换
    {
      from: Chat4SceneId.PHONE,
      to: Chat4SceneId.CONCERT,
      action: Chat4Action.GO_TO_CONCERT,
      clearMessages: true,
    },

    // 从演唱会场景到其他场景
    {
      from: Chat4SceneId.CONCERT,
      to: Chat4SceneId.PHONE,
      action: Chat4Action.GO_TO_CHAT,
      clearMessages: true,
    },

    // 从日记场景到其他场景
    {
      from: Chat4SceneId.DIARY,
      to: Chat4SceneId.PHONE,
      action: Chat4Action.GO_TO_CHAT,
      clearMessages: true,
    },

    // 通用GO_TO_MAP规则（从任意meetup子场景到地图）
    {
      from: '*',
      to: Chat4SceneId.MAP,
      action: Chat4Action.GO_TO_MAP,
      guard: () => {
        // 支持所有以 'map-' 开头的自定义meetup子场景
        const currentScene = state.value.currentScene
        return (
          typeof currentScene === 'string' && currentScene.startsWith('map-')
        )
      },
      clearMessages: true, // 返回地图时清空消息
    },

    // 通用GO_TO_CHAT规则（从任意场景到聊天室）
    {
      from: '*',
      to: Chat4SceneId.PHONE,
      action: Chat4Action.GO_TO_CHAT,
      guard: () =>
        !!storyStore.currentStory?.id && !!storyStore.currentActor?.id,
      clearMessages: true, // 进入聊天室时清空消息
      effect: () => {
        const scenePayment = useScenePayment()
        scenePayment.fetchSceneOptions().catch((error) => {
          console.error('Failed to load scene payment options:', error)
        })
      },
    },

    // 通用返回规则
    {
      from: '*',
      to: Chat4SceneId.LIVING, // 占位符，实际会被动态计算
      action: Chat4Action.BACK_TO_PREVIOUS,
      guard: () => state.value.canGoBack,
      clearMessages: true, // 返回上一场景时不清空消息
    },

    // 服务器场景变化
    {
      from: '*',
      to: Chat4SceneId.LIVING, // 占位符，实际会被动态设置
      action: Chat4Action.SERVER_SCENE_CHANGE,
      clearMessages: true, // 服务器主动场景变化时清空消息
    },

    // 错误处理
    {
      from: '*',
      to: Chat4SceneId.LIVING,
      action: Chat4Action.RESET_TO_DEFAULT,
      clearMessages: true, // 重置到默认状态时清空消息
    },
  ]

  // 计算属性
  const currentSceneType = computed(() => {
    return Chat4SceneUtils.getSceneUIType(state.value.currentScene)
  })

  const isLiveScene = computed(() => {
    return Chat4SceneUtils.isLivingScene(state.value.currentScene)
  })

  const isChatScene = computed(() => {
    return Chat4SceneUtils.isPhoneScene(state.value.currentScene)
  })

  // 场景切换核心方法
  const transition = async (
    action: Chat4Action,
    targetScene?: Chat4SceneState,
  ) => {
    // 防止重复转换
    if (state.value.isTransitioning) {
      console.warn('Transition already in progress, ignoring action:', action)
      return false
    }

    // 查找匹配的转换规则
    const applicableTransitions = transitions.filter(
      (t) =>
        (t.from === '*' || t.from === state.value.currentScene) &&
        t.action === action,
    )

    if (applicableTransitions.length === 0) {
      console.warn('No transition found for action:', action)
      return false
    }

    // 优先使用具体的规则（非通用规则）
    const specificTransition = applicableTransitions.find((t) => t.from !== '*')
    const universalTransition = applicableTransitions.find(
      (t) => t.from === '*',
    )
    const transition = specificTransition || applicableTransitions[0]

    // 检查守卫条件
    if (transition.guard && !transition.guard()) {
      console.warn('Transition guard failed for action:', action)
      return false
    }

    // 确定目标场景
    let finalTargetScene: Chat4SceneState
    if (action === Chat4Action.BACK_TO_PREVIOUS) {
      finalTargetScene = state.value.previousScene || Chat4SceneId.LIVING
    } else if (action === Chat4Action.SERVER_SCENE_CHANGE && targetScene) {
      finalTargetScene = targetScene
    } else if (action === Chat4Action.GO_TO_MEETUP && targetScene) {
      // 对于GO_TO_MEETUP动作，如果提供了targetScene，使用它而不是静态的转换规则
      finalTargetScene = targetScene
    } else {
      finalTargetScene = transition.to
    }

    // 开始转换
    state.value.isTransitioning = true
    state.value.error = null

    // 如果需要服务器场景跳转，显示 loading
    if (shouldJumpToServer(action)) {
      state.value.isLoading = true
      state.value.loadingType = 'scene-transition'

      // 通知Chat4 Store开始场景切换
      const chat4Store = useChat4Store()
      chat4Store.setSceneLoading(true, 'scene-transition')
    }

    try {
      // 执行副作用 - 先执行具体规则的effect
      if (transition.effect) {
        await transition.effect()
      }

      // 如果有通用规则且其effect与具体规则不同，也执行通用规则的effect
      if (
        universalTransition &&
        universalTransition.effect &&
        universalTransition.effect !== transition.effect
      ) {
        await universalTransition.effect()
      }

      // 如果需要服务器场景跳转
      if (shouldJumpToServer(action)) {
        // 对于GO_TO_MEETUP动作，传递locationKey作为额外参数
        const locationKey =
          action === Chat4Action.GO_TO_MEETUP && targetScene
            ? targetScene
            : undefined
        await performServerSceneJump(
          finalTargetScene,
          transition.clearMessages,
          locationKey,
        )
      } else {
        // 如果不需要服务器跳转，但配置了清空消息，则立即清空
        if (transition.clearMessages) {
          chatMessagesStore.clearMessages()
        }
      }

      // 更新状态
      updateState(finalTargetScene)

      // 上报事件
      reportSceneTransition(action, finalTargetScene)

      return true
    } catch (error) {
      console.error('Scene transition failed:', error)
      state.value.error =
        error instanceof Error ? error.message : 'Unknown error'
      return false
    } finally {
      state.value.isTransitioning = false
      state.value.isLoading = false

      // 通知Chat4 Store场景切换完成
      const chat4Store = useChat4Store()
      chat4Store.setSceneLoading(false)

      // 确保TTS播放被恢复（防止服务器场景跳转没有正确完成的情况）
      chat4Store.resumeTTSPlayback()
    }
  }

  // 判断是否需要服务器场景跳转
  const shouldJumpToServer = (action: Chat4Action): boolean => {
    return [
      Chat4Action.GO_TO_CHAT,
      Chat4Action.GO_TO_LIVE,
      Chat4Action.ACCEPT_FRIEND_REQUEST,
      Chat4Action.GO_TO_VIDEO,
      Chat4Action.GO_TO_MONITOR,
      Chat4Action.GO_TO_MAP,
      Chat4Action.GO_TO_MEETUP,
      Chat4Action.GO_TO_DANCING,
      Chat4Action.GO_TO_CONCERT,
      Chat4Action.GO_TO_MOMENT,
      Chat4Action.GO_TO_DIARY,
    ].includes(action)
  }

  // 执行服务器场景跳转
  const performServerSceneJump = async (
    targetScene: Chat4SceneState,
    clearMessages?: boolean,
    locationKey?: string,
  ): Promise<void> => {
    const storyId = storyStore.currentStory?.id
    const actorId = storyStore.currentActor?.id

    if (!storyId) {
      throw new Error('No story ID available for scene jump')
    }

    return new Promise((resolve, reject) => {
      let hasReceivedFirstEvent = false

      // 获取Chat4 Store引用，但暂时不更新场景状态
      const chat4Store = useChat4Store()

      jumpToSceneSSE(
        storyId,
        locationKey || targetScene,
        async (data) => {
          const parsedData = typeof data === 'string' ? JSON.parse(data) : data
          if (parsedData) {
            // 在收到第一个服务器事件时进行初始化
            if (!hasReceivedFirstEvent) {
              // 先清空旧场景消息（如果需要）
              if (clearMessages) {
                chatMessagesStore.clearMessages()
              }
              // Phone场景，加载聊天历史
              if (Chat4SceneUtils.isPhoneScene(targetScene)) {
                const { loadChat4History } = useChat4Messages()
                loadChat4History('Phone')
              }

              // 然后更新Chat4 Store的场景状态，但暂时阻止TTS播放
              chat4Store.setCurrentScene(targetScene)

              // 同时更新状态机的状态
              updateState(targetScene)

              hasReceivedFirstEvent = true
            }

            try {
              // 判断是否是从Phone场景跳转到直播间场景
              const isPhoneToLiveTransition =
                state.value.currentScene === Chat4SceneId.PHONE &&
                targetScene === Chat4SceneId.LIVING

              if (isPhoneToLiveTransition) {
                // 从Phone场景跳转到直播间场景时，不处理message事件
                if (parsedData.event_type !== 'message') {
                  await chatEventsStore.handleEvent(parsedData)
                }
              } else {
                // 其他场景跳转正常处理所有事件
                await chatEventsStore.handleEvent(parsedData)
              }
            } catch (error) {
              console.error(
                'chat4-state-machine: Error processing server event:',
                parsedData.event_type,
                error,
              )
              // 继续处理后续事件，不要因为一个事件失败就停止整个流程
            }
          }
        },
        (error) => {
          console.error('Server scene jump failed:', error)
          reject(error)
        },
        async () => {
          // 场景跳转完成后，处理事件队列
          await chatEventsStore.processEventQueue()

          // 确保状态机状态与目标场景同步
          updateState(targetScene)

          // 恢复TTS播放
          chat4Store.resumeTTSPlayback()

          resolve()
        },
        actorId,
      )
    })
  }

  // 更新状态
  const updateState = (newScene: Chat4SceneState) => {
    const oldScene = state.value.currentScene

    // 更新场景历史
    if (oldScene !== newScene) {
      state.value.previousScene = oldScene
      state.value.sceneHistory.push(newScene)

      // 限制历史记录长度
      if (state.value.sceneHistory.length > 10) {
        state.value.sceneHistory.shift()
      }
    }

    // 更新当前场景
    state.value.currentScene = newScene

    // 更新是否可以返回
    state.value.canGoBack = state.value.sceneHistory.length > 1
  }

  // 上报场景切换事件
  const reportSceneTransition = (
    action: Chat4Action,
    targetScene: Chat4SceneState,
  ) => {
    reportEvent(ReportEvent.SceneChange, {
      userId: userStore.userInfo?.uuid,
      actorId: storyStore.currentActor?.id,
      storyId: storyStore.currentStory?.id,
      action,
      fromScene: state.value.previousScene,
      toScene: targetScene,
      timestamp: Date.now(),
    })
  }

  return {
    // 状态
    state: computed(() => state.value),
    currentScene: computed(() => state.value.currentScene),
    isTransitioning: computed(() => state.value.isTransitioning),
    isLoading: computed(() => state.value.isLoading),
    loadingType: computed(() => state.value.loadingType),
    loadingTitle: computed(() => {
      if (state.value.loadingTitle) return state.value.loadingTitle
      if (state.value.loadingType === 'connection') return 'Connecting'
      return state.value.loadingTitle
    }),
    loadingSubtitle: computed(() => {
      if (state.value.loadingSubtitle) return state.value.loadingSubtitle
      if (state.value.loadingType === 'connection')
        return 'Establishing connection...'
      return state.value.loadingSubtitle
    }),
    error: computed(() => state.value.error),
    canGoBack: computed(() => state.value.canGoBack),

    // 计算属性
    currentSceneType,
    isLiveScene,
    isChatScene,

    // 方法
    transition,
    performServerSceneJump,

    // 便捷方法
    goToLive: () => transition(Chat4Action.GO_TO_LIVE),
    goToChat: () => transition(Chat4Action.GO_TO_CHAT),
    goToVideo: () => transition(Chat4Action.GO_TO_VIDEO),
    goToMonitor: () => transition(Chat4Action.GO_TO_MONITOR),
    goToMap: () => transition(Chat4Action.GO_TO_MAP),
    goToMeetup: (targetScene?: Chat4SceneState) => {
      if (targetScene) {
        // 直接切换到指定的meetup子场景
        state.value.currentScene = targetScene
        reportSceneTransition(Chat4Action.GO_TO_MEETUP, targetScene)
      } else {
        transition(Chat4Action.GO_TO_MEETUP)
      }
    },
    goToDancing: () => transition(Chat4Action.GO_TO_DANCING),
    goToConcert: () => transition(Chat4Action.GO_TO_CONCERT),
    goToTip: () => transition(Chat4Action.GO_TO_TIP),
    goToMoment: () => transition(Chat4Action.GO_TO_MOMENT),
    goToDiary: () => transition(Chat4Action.GO_TO_DIARY),
    acceptFriendRequest: () => transition(Chat4Action.ACCEPT_FRIEND_REQUEST),
    hangupVideo: () => transition(Chat4Action.GO_TO_CHAT),
    goBack: () => transition(Chat4Action.BACK_TO_PREVIOUS),

    // 服务器事件处理
    handleServerSceneChange: (sceneId: string) => {
      if (Chat4SceneUtils.isValidChat4Scene(sceneId)) {
        state.value.serverSceneId = sceneId

        // 如果是初始化阶段（正在加载），直接设置场景而不是转换
        if (state.value.isLoading && state.value.loadingType === 'connection') {
          state.value.currentScene = sceneId as Chat4SceneState
          state.value.sceneHistory = [sceneId as Chat4SceneState]

          if (Chat4SceneUtils.isPhoneScene(sceneId)) {
            const scenePayment = useScenePayment()
            scenePayment.fetchSceneOptions().catch((error) => {
              console.error('Failed to load scene payment options:', error)
            })
          }
          
          // 延迟一点时间让用户看到加载完成，然后隐藏加载界面
          setTimeout(() => {
            state.value.isLoading = false
            state.value.loadingType = 'scene-transition'
          }, 800) // 800ms延迟，让加载动画完成
          return Promise.resolve(true)
        }

        // 正常的场景切换
        const result = transition(
          Chat4Action.SERVER_SCENE_CHANGE,
          sceneId as Chat4SceneState,
        )

        return result
      }

      return Promise.resolve(false)
    },

    // 错误处理
    resetToDefault: () => transition(Chat4Action.RESET_TO_DEFAULT),
    clearError: () => {
      state.value.error = null
    },

    // 消息管理
    clearMessages: () => {
      chatMessagesStore.clearMessages()
    },

    // 初始化完成，隐藏加载界面
    completeInitialization: () => {
      state.value.isLoading = false
      state.value.loadingType = 'scene-transition'
    },

    // 设置初始场景（用于避免默认场景闪烁）
    setInitialScene: (sceneId: Chat4SceneState) => {
      state.value.currentScene = sceneId
      state.value.sceneHistory = [sceneId]
    },
  }
}
